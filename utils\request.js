import { useUserStore } from '@/store'
import config from '@/config'
import { getToken } from '@/utils/auth'
import errorCode from '@/utils/errorCode'
import { toast, showConfirm, tansParams } from '@/utils/common'

let timeout = 10000
const baseUrl = config.baseUrl

const request = config => {
  // 是否需要设置 token
  const isToken = (config.headers || {}).isToken === false
  config.header = config.header || {}
  if (getToken() && !isToken) {
    config.header['Authorization'] = 'Bearer ' + getToken()
  }
  // get请求映射params参数
  if (config.params) {
    let url = config.url + '?' + tansParams(config.params)
    url = url.slice(0, -1)
    config.url = url
  }
  return new Promise((resolve, reject) => {
    uni.request({
        method: config.method || 'get',
        timeout: config.timeout ||  timeout,
        url: config.baseUrl || baseUrl + config.url,
        data: config.data,
        header: config.header,
        dataType: 'json'
      }).then(response => {
        console.log('请求响应:', response)
        const res = response.data.data
        const code = res?.code || response.statusCode || 200
        const msg = res?.msg || errorCode[code] || errorCode['default']

        if (code === 401) {
          showConfirm('登录状态已过期，您可以继续留在该页面，或者重新登录?').then(confirmRes => {
            if (confirmRes.confirm) {
              useUserStore().logOut().then(() => {
                uni.reLaunch({ url: '/pages/login' })
              })
            }
          })
          reject({ msg: '无效的会话，或者会话已过期，请重新登录。', code: 401 })
        } else if (code === 500) {
          toast(msg)
          reject({ msg, code: 500 })
        } else if (code !== 200) {
          // 不在这里显示toast，让调用方处理
          reject({ msg, code })
        } else {
          // 成功的情况，返回完整的响应数据
          resolve(res)
        }
      })
      .catch(error => {
        let { message } = error
        if (message === 'Network Error') {
          message = '后端接口连接异常'
        } else if (message.includes('timeout')) {
          message = '系统接口请求超时'
        } else if (message.includes('Request failed with status code')) {
          message = '系统接口' + message.substr(message.length - 3) + '异常'
        }
        toast(message)
        reject(error)
      })
  })
}

export default request

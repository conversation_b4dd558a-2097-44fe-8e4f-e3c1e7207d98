/* 通用文本样式 */
.text-center {
	text-align: center;
}

.font-13 {
	font-size: 13px;
}

.font-12 {
	font-size: 12px;
}

.font-11 {
	font-size: 11px;
}

.text-grey1 {
	color: #888;
}

.text-grey2 {
	color: #aaa;
}

/* 现代化卡片样式 */
.modern-card {
  background: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.05);
  overflow: hidden;
  transition: all 0.3s ease;
}

.modern-card:hover {
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
  transform: translateY(-2rpx);
}

/* 渐变背景 */
.gradient-bg-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.gradient-bg-secondary {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.gradient-bg-success {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

/* 按钮样式 */
.modern-button {
  border-radius: 12rpx;
  border: none;
  font-weight: 600;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modern-button:active {
  transform: scale(0.98);
}

.modern-button-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #ffffff;
}

.modern-button-secondary {
  background: #f8f9fa;
  color: #333333;
  border: 2rpx solid #e9ecef;
}

/* 输入框样式 */
.modern-input {
  background: #f8f9fa;
  border: 2rpx solid transparent;
  border-radius: 12rpx;
  padding: 24rpx;
  font-size: 28rpx;
  transition: all 0.3s ease;
}

.modern-input:focus {
  border-color: #667eea;
  background: #ffffff;
  box-shadow: 0 0 0 6rpx rgba(102,126,234,0.1);
}

/* 图标容器 */
.icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12rpx;
  transition: all 0.3s ease;
}

.icon-container-sm {
  width: 60rpx;
  height: 60rpx;
}

.icon-container-md {
  width: 80rpx;
  height: 80rpx;
}

.icon-container-lg {
  width: 100rpx;
  height: 100rpx;
}

/* 列表样式 */
.list-cell-arrow::before {
    content: ' ';
    height: 10px;
    width: 10px;
    border-width: 2px 2px 0 0;
    border-color: #c0c0c0;
    border-style: solid;
    -webkit-transform: matrix(0.5, 0.5, -0.5, 0.5, 0, 0);
    transform: matrix(0.5, 0.5, -0.5, 0.5, 0, 0);
    position: absolute;
    top: 50%;
    margin-top: -6px;
    right: 30rpx;
}

.list-cell {
    position: relative;
    width: 100%;
    box-sizing: border-box;
    background-color: #fff;
    color: #333;
    padding: 26rpx 30rpx;
    transition: all 0.3s ease;
}

.list-cell:active {
    background-color: #f8f9fa;
}

.list-cell:first-child {
    border-radius: 16rpx 16rpx 0 0;
}

.list-cell:last-child {
    border-radius: 0 0 16rpx 16rpx;
}

.list-cell::after {
    content: '';
    position: absolute;
    border-bottom: 1px solid #f0f0f0;
    -webkit-transform: scaleY(0.5) translateZ(0);
    transform: scaleY(0.5) translateZ(0);
    transform-origin: 0 100%;
    bottom: 0;
    right: 0;
    left: 0;
    pointer-events: none;
}

.menu-list {
    margin: 15px 15px;

    .menu-item-box {
      width: 100%;
      display: flex;
      align-items: center;

      .menu-icon {
        color: #667eea;
        font-size: 16px;
        margin-right: 5px;
      }

      .text-right {
        margin-left: auto;
        margin-right: 34rpx;
        color: #999;
      }
    }
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

.fade-in {
  animation: fadeIn 0.4s ease-out;
}

/* 响应式设计 */
@media screen and (min-width: 768px) {
  .container {
    max-width: 600px;
    margin: 0 auto;
  }
}

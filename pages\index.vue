<template>
  <view class="home-container">
    <!-- 顶部欢迎区域 -->
    <view class="welcome-section">
      <view class="welcome-bg"></view>
      <view class="welcome-content">
        <image class="logo" src="@/static/logo.png"></image>
        <view class="welcome-text">
          <text class="title">欢迎使用</text>
          <text class="subtitle">RuoYi移动端框架</text>
        </view>
      </view>
    </view>

    <!-- 功能卡片区域 -->
    <view class="feature-section">
      <view class="section-title">
        <text>核心功能</text>
      </view>
      <view class="feature-grid">
        <view class="feature-card" @click="navigateToWork">
          <view class="card-icon">
            <uni-icons type="gear-filled" size="32" color="#4A90E2"></uni-icons>
          </view>
          <view class="card-content">
            <text class="card-title">系统管理</text>
            <text class="card-desc">用户、角色、菜单管理</text>
          </view>
        </view>

        <view class="feature-card" @click="navigateToMine">
          <view class="card-icon">
            <uni-icons type="person-filled" size="32" color="#50C878"></uni-icons>
          </view>
          <view class="card-content">
            <text class="card-title">个人中心</text>
            <text class="card-desc">个人信息、设置管理</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 统计信息区域 -->
    <view class="stats-section">
      <view class="section-title">
        <text>数据概览</text>
      </view>
      <view class="stats-grid">
        <view class="stat-item">
          <text class="stat-number">1,234</text>
          <text class="stat-label">用户总数</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">56</text>
          <text class="stat-label">在线用户</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">789</text>
          <text class="stat-label">今日访问</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { getCurrentInstance } from "vue"

const { proxy } = getCurrentInstance()

function navigateToWork() {
  uni.switchTab({
    url: '/pages/work/index'
  })
}

function navigateToMine() {
  uni.switchTab({
    url: '/pages/mine/index'
  })
}
</script>

<style lang="scss" scoped>
.home-container {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 90vh;

  .welcome-section {
    position: relative;
    height: 400rpx;
    overflow: hidden;

    .welcome-bg {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);

      &::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
        animation: float 6s ease-in-out infinite;
      }
    }

    .welcome-content {
      position: relative;
      z-index: 2;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100%;
      padding: 40rpx;

      .logo {
        width: 120rpx;
        height: 120rpx;
        border-radius: 24rpx;
        box-shadow: 0 8rpx 32rpx rgba(0,0,0,0.1);
        margin-bottom: 30rpx;
      }

      .welcome-text {
        text-align: center;

        .title {
          display: block;
          font-size: 48rpx;
          font-weight: 600;
          color: #ffffff;
          margin-bottom: 10rpx;
        }

        .subtitle {
          display: block;
          font-size: 28rpx;
          color: rgba(255,255,255,0.8);
        }
      }
    }
  }

  .feature-section, .stats-section {
    padding: 40rpx 30rpx;

    .section-title {
      margin-bottom: 30rpx;

      text {
        font-size: 32rpx;
        font-weight: 600;
        color: #ffffff;
      }
    }
  }

  .feature-grid {
    display: flex;
    flex-direction: column;
    gap: 20rpx;

    .feature-card {
      background: rgba(255,255,255,0.95);
      border-radius: 16rpx;
      padding: 30rpx;
      display: flex;
      align-items: center;
      box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
      transition: all 0.3s ease;

      &:active {
        transform: scale(0.98);
        box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.15);
      }

      .card-icon {
        width: 80rpx;
        height: 80rpx;
        background: rgba(74,144,226,0.1);
        border-radius: 16rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 24rpx;
      }

      .card-content {
        flex: 1;

        .card-title {
          display: block;
          font-size: 32rpx;
          font-weight: 600;
          color: #333333;
          margin-bottom: 8rpx;
        }

        .card-desc {
          display: block;
          font-size: 24rpx;
          color: #666666;
        }
      }
    }
  }

  .stats-grid {
    display: flex;
    justify-content: space-between;
    background: rgba(255,255,255,0.95);
    border-radius: 16rpx;
    padding: 30rpx;
    box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);

    .stat-item {
      flex: 1;
      text-align: center;

      .stat-number {
        display: block;
        font-size: 40rpx;
        font-weight: 700;
        color: #4A90E2;
        margin-bottom: 8rpx;
      }

      .stat-label {
        display: block;
        font-size: 24rpx;
        color: #666666;
      }
    }
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}
</style>

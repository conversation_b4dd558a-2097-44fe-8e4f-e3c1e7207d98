<template>
  <view class="work-container">
    <!-- 轮播图区域 -->
    <view class="banner-section">
      <uni-swiper-dot class="uni-swiper-dot-box" :info="data" :current="current" field="content">
        <swiper class="swiper-box" :current="swiperDotIndex" @change="changeSwiper" autoplay circular>
          <swiper-item v-for="(item, index) in data" :key="index">
            <view class="swiper-item" @click="clickBannerItem(item)">
              <image :src="item.image" mode="aspectFill" :draggable="false" />
              <view class="banner-overlay">
                <text class="banner-title">{{ item.title || '系统管理平台' }}</text>
                <text class="banner-desc">{{ item.desc || '高效便捷的移动端管理体验' }}</text>
              </view>
            </view>
          </swiper-item>
        </swiper>
      </uni-swiper-dot>
    </view>

    <!-- 快捷操作区域 -->
    <view class="quick-actions">
      <view class="section-header">
        <text class="section-title">快捷操作</text>
        <text class="section-subtitle">常用功能快速入口</text>
      </view>
      <view class="actions-grid">
        <view class="action-item" @click="changeGrid">
          <view class="action-icon">
            <uni-icons type="person-filled" size="24" color="#4A90E2"></uni-icons>
          </view>
          <text class="action-text">用户管理</text>
        </view>
        <view class="action-item" @click="changeGrid">
          <view class="action-icon">
            <uni-icons type="staff-filled" size="24" color="#50C878"></uni-icons>
          </view>
          <text class="action-text">角色管理</text>
        </view>
        <view class="action-item" @click="changeGrid">
          <view class="action-icon">
            <uni-icons type="color" size="24" color="#FF6B6B"></uni-icons>
          </view>
          <text class="action-text">菜单管理</text>
        </view>
        <view class="action-item" @click="changeGrid">
          <view class="action-icon">
            <uni-icons type="settings-filled" size="24" color="#FFD93D"></uni-icons>
          </view>
          <text class="action-text">部门管理</text>
        </view>
      </view>
    </view>

    <!-- 系统管理区域 -->
    <view class="system-management">
      <view class="section-header">
        <text class="section-title">系统管理</text>
        <text class="section-subtitle">完整的系统管理功能</text>
      </view>
      <view class="management-grid">
        <view class="management-card" @click="changeGrid">
          <view class="card-icon">
            <uni-icons type="heart-filled" size="28" color="#E74C3C"></uni-icons>
          </view>
          <view class="card-content">
            <text class="card-title">岗位管理</text>
            <text class="card-desc">组织架构管理</text>
          </view>
        </view>
        <view class="management-card" @click="changeGrid">
          <view class="card-icon">
            <uni-icons type="bars" size="28" color="#9B59B6"></uni-icons>
          </view>
          <view class="card-content">
            <text class="card-title">字典管理</text>
            <text class="card-desc">数据字典维护</text>
          </view>
        </view>
        <view class="management-card" @click="changeGrid">
          <view class="card-icon">
            <uni-icons type="gear-filled" size="28" color="#3498DB"></uni-icons>
          </view>
          <view class="card-content">
            <text class="card-title">参数设置</text>
            <text class="card-desc">系统参数配置</text>
          </view>
        </view>
        <view class="management-card" @click="changeGrid">
          <view class="card-icon">
            <uni-icons type="chat-filled" size="28" color="#F39C12"></uni-icons>
          </view>
          <view class="card-content">
            <text class="card-title">通知公告</text>
            <text class="card-desc">消息通知管理</text>
          </view>
        </view>
        <view class="management-card" @click="changeGrid">
          <view class="card-icon">
            <uni-icons type="wallet-filled" size="28" color="#27AE60"></uni-icons>
          </view>
          <view class="card-content">
            <text class="card-title">日志管理</text>
            <text class="card-desc">系统日志查看</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
  import { ref, getCurrentInstance } from "vue"

  const { proxy } = getCurrentInstance()
  const current = ref(0)
  const swiperDotIndex = ref(0)
  const data = ref([
    {
      image: '/static/images/banner/banner01.jpg',
      title: '系统管理平台',
      desc: '高效便捷的移动端管理体验'
    },
    {
      image: '/static/images/banner/banner02.jpg',
      title: '数据统计分析',
      desc: '实时数据监控与分析'
    },
    {
      image: '/static/images/banner/banner03.jpg',
      title: '智能运维管理',
      desc: '自动化运维解决方案'
    }
  ])

  function clickBannerItem(item) {
    console.info(item)
    proxy.$modal.showToast(`点击了：${item.title}`)
  }

  function changeSwiper(e) {
    current.value = e.detail.current
    swiperDotIndex.value = e.detail.current
  }

  function changeGrid(e) {
    proxy.$modal.showToast('功能开发中，敬请期待~')
  }
</script>

<style lang="scss" scoped>
page {
  background-color: #f8f9fa;
}

.work-container {
  min-height: 100vh;

  .banner-section {
    margin: 20rpx;
    border-radius: 16rpx;
    overflow: hidden;
    box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);

    .uni-swiper-dot-box {
      border-radius: 16rpx;
      overflow: hidden;
    }

    .swiper-box {
      height: 320rpx;
      border-radius: 16rpx;
    }

    .swiper-item {
      position: relative;
      height: 320rpx;
      border-radius: 16rpx;
      overflow: hidden;

      image {
        width: 100%;
        height: 100%;
        border-radius: 16rpx;
      }

      .banner-overlay {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        background: linear-gradient(transparent, rgba(0,0,0,0.6));
        padding: 40rpx 30rpx 30rpx;

        .banner-title {
          display: block;
          color: #ffffff;
          font-size: 32rpx;
          font-weight: 600;
          margin-bottom: 8rpx;
        }

        .banner-desc {
          display: block;
          color: rgba(255,255,255,0.8);
          font-size: 24rpx;
        }
      }
    }
  }

  .quick-actions {
    margin: 30rpx 20rpx;
    background: #ffffff;
    border-radius: 16rpx;
    padding: 30rpx;
    box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.05);

    .section-header {
      margin-bottom: 30rpx;

      .section-title {
        display: block;
        font-size: 32rpx;
        font-weight: 600;
        color: #333333;
        margin-bottom: 8rpx;
      }

      .section-subtitle {
        display: block;
        font-size: 24rpx;
        color: #666666;
      }
    }

    .actions-grid {
      display: flex;
      justify-content: space-between;

      .action-item {
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 20rpx 10rpx;
        border-radius: 12rpx;
        transition: all 0.3s ease;

        &:active {
          background-color: #f8f9fa;
          transform: scale(0.95);
        }

        .action-icon {
          width: 80rpx;
          height: 80rpx;
          background: rgba(74,144,226,0.1);
          border-radius: 16rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-bottom: 16rpx;
        }

        .action-text {
          font-size: 24rpx;
          color: #333333;
          text-align: center;
        }
      }
    }
  }

  .system-management {
    margin: 0 20rpx 30rpx;
    background: #ffffff;
    border-radius: 16rpx;
    padding: 30rpx;
    box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.05);

    .section-header {
      margin-bottom: 30rpx;

      .section-title {
        display: block;
        font-size: 32rpx;
        font-weight: 600;
        color: #333333;
        margin-bottom: 8rpx;
      }

      .section-subtitle {
        display: block;
        font-size: 24rpx;
        color: #666666;
      }
    }

    .management-grid {
      display: flex;
      flex-wrap: wrap;
      gap: 20rpx;

      .management-card {
        width: calc(50% - 10rpx);
        background: #f8f9fa;
        border-radius: 12rpx;
        padding: 24rpx;
        display: flex;
        align-items: center;
        transition: all 0.3s ease;

        &:active {
          background-color: #e9ecef;
          transform: scale(0.98);
        }

        .card-icon {
          width: 60rpx;
          height: 60rpx;
          background: #ffffff;
          border-radius: 12rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 20rpx;
          box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
        }

        .card-content {
          flex: 1;

          .card-title {
            display: block;
            font-size: 28rpx;
            font-weight: 600;
            color: #333333;
            margin-bottom: 6rpx;
          }

          .card-desc {
            display: block;
            font-size: 22rpx;
            color: #666666;
          }
        }
      }
    }
  }
}

@media screen and (min-width: 500px) {
  .uni-swiper-dot-box {
    width: 400px;
    margin: 0 auto;
    margin-top: 8px;
  }
}
</style>

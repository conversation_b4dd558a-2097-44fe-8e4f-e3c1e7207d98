<template>
  <view class="mine-container">
    <!--顶部个人信息栏-->
    <view class="header-section">
      <view class="header-bg"></view>
      <view class="header-content">
        <view class="user-avatar-section">
          <view v-if="!avatar" class="avatar-placeholder" @click="handleToLogin">
            <view class="iconfont icon-people avatar-icon"></view>
          </view>
          <image v-if="avatar" @click="handleToAvatar" :src="avatar" class="user-avatar" mode="aspectFill">
          </image>
          <view class="avatar-badge" v-if="name">
            <uni-icons type="checkmarkempty" size="16" color="#ffffff"></uni-icons>
          </view>
        </view>

        <view class="user-info-section">
          <view v-if="!name" @click="handleToLogin" class="login-prompt">
            <text class="login-text">点击登录</text>
            <text class="login-desc">登录后享受更多功能</text>
          </view>
          <view v-if="name" class="user-details">
            <text class="user-name">{{ name }}</text>
            <text class="user-role">系统管理员</text>
          </view>
        </view>

        <view @click="handleToInfo" class="profile-btn">
          <uni-icons type="right" size="18" color="rgba(255,255,255,0.8)"></uni-icons>
        </view>
      </view>
    </view>

    <view class="content-section">
      <!-- 快捷操作卡片 -->
      <view class="quick-actions-card">
        <view class="actions-grid">
          <view class="action-item" @click="handleJiaoLiuQun">
            <view class="action-icon">
              <uni-icons type="chat-filled" size="24" color="#FF6B9D"></uni-icons>
            </view>
            <text class="action-text">交流群</text>
          </view>
          <view class="action-item" @click="handleBuilding">
            <view class="action-icon">
              <uni-icons type="headphones" size="24" color="#4ECDC4"></uni-icons>
            </view>
            <text class="action-text">在线客服</text>
          </view>
          <view class="action-item" @click="handleBuilding">
            <view class="action-icon">
              <uni-icons type="heart-filled" size="24" color="#45B7D1"></uni-icons>
            </view>
            <text class="action-text">反馈社区</text>
          </view>
          <view class="action-item" @click="handleBuilding">
            <view class="action-icon">
              <uni-icons type="star-filled" size="24" color="#FFA726"></uni-icons>
            </view>
            <text class="action-text">点赞我们</text>
          </view>
        </view>
      </view>

      <!-- 功能菜单 -->
      <view class="function-menu">
        <view class="menu-section">
          <text class="section-title">个人中心</text>
          <view class="menu-items">
            <view class="menu-item" @click="handleToEditInfo">
              <view class="menu-item-content">
                <view class="menu-icon">
                  <uni-icons type="person-filled" size="20" color="#4A90E2"></uni-icons>
                </view>
                <text class="menu-text">编辑资料</text>
              </view>
              <uni-icons type="right" size="16" color="#C0C0C0"></uni-icons>
            </view>
            <view class="menu-item" @click="handleToSetting">
              <view class="menu-item-content">
                <view class="menu-icon">
                  <uni-icons type="gear-filled" size="20" color="#50C878"></uni-icons>
                </view>
                <text class="menu-text">应用设置</text>
              </view>
              <uni-icons type="right" size="16" color="#C0C0C0"></uni-icons>
            </view>
          </view>
        </view>

        <view class="menu-section">
          <text class="section-title">帮助与支持</text>
          <view class="menu-items">
            <view class="menu-item" @click="handleHelp">
              <view class="menu-item-content">
                <view class="menu-icon">
                  <uni-icons type="help-filled" size="20" color="#FF6B6B"></uni-icons>
                </view>
                <text class="menu-text">常见问题</text>
              </view>
              <uni-icons type="right" size="16" color="#C0C0C0"></uni-icons>
            </view>
            <view class="menu-item" @click="handleAbout">
              <view class="menu-item-content">
                <view class="menu-icon">
                  <uni-icons type="info-filled" size="20" color="#9B59B6"></uni-icons>
                </view>
                <text class="menu-text">关于我们</text>
              </view>
              <uni-icons type="right" size="16" color="#C0C0C0"></uni-icons>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
  import { useUserStore } from '@/store'
  import { computed , getCurrentInstance } from "vue"

  const { proxy } = getCurrentInstance()
  const name = useUserStore().name
  const avatar = computed(() => useUserStore().avatar)
  const windowHeight = computed(() => uni.getSystemInfoSync().windowHeight - 50)

  function handleToInfo() {
    proxy.$tab.navigateTo('/pages/mine/info/index')
  }

  function handleToEditInfo() {
    proxy.$tab.navigateTo('/pages/mine/info/edit')
  }

  function handleToSetting() {
    proxy.$tab.navigateTo('/pages/mine/setting/index')
  }

  function handleToLogin() {
    proxy.$tab.reLaunch('/pages/login')
  }

  function handleToAvatar() {
    proxy.$tab.navigateTo('/pages/mine/avatar/index')
  }
      
  function handleHelp() {
    proxy.$tab.navigateTo('/pages/mine/help/index')
  }
      
  function handleAbout() {
    proxy.$tab.navigateTo('/pages/mine/about/index')
  }
      
  function handleJiaoLiuQun() {
    proxy.$modal.showToast('QQ群：①133713780(满)、②146013835(满)、③189091635')
  }
      
  function handleBuilding() {
    proxy.$modal.showToast('模块建设中~')
  }
</script>

<style lang="scss" scoped>
page {
  background-color: #f8f9fa;
}

.mine-container {
  min-height: 100vh;

  .header-section {
    position: relative;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 60rpx 30rpx 80rpx;
    overflow: hidden;

    .header-bg {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: radial-gradient(circle at 20% 80%, rgba(255,255,255,0.1) 0%, transparent 50%),
                  radial-gradient(circle at 80% 20%, rgba(255,255,255,0.1) 0%, transparent 50%);
    }

    .header-content {
      position: relative;
      z-index: 2;
      display: flex;
      align-items: center;

      .user-avatar-section {
        position: relative;
        margin-right: 30rpx;

        .avatar-placeholder {
          width: 120rpx;
          height: 120rpx;
          background: rgba(255,255,255,0.2);
          border-radius: 60rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          border: 3rpx solid rgba(255,255,255,0.3);

          .avatar-icon {
            font-size: 60rpx;
            color: rgba(255,255,255,0.8);
          }
        }

        .user-avatar {
          width: 120rpx;
          height: 120rpx;
          border-radius: 60rpx;
          border: 3rpx solid rgba(255,255,255,0.3);
        }

        .avatar-badge {
          position: absolute;
          bottom: 0;
          right: 0;
          width: 36rpx;
          height: 36rpx;
          background: #4CAF50;
          border-radius: 18rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          border: 3rpx solid #ffffff;
        }
      }

      .user-info-section {
        flex: 1;

        .login-prompt {
          .login-text {
            display: block;
            font-size: 36rpx;
            font-weight: 600;
            color: #ffffff;
            margin-bottom: 8rpx;
          }

          .login-desc {
            display: block;
            font-size: 24rpx;
            color: rgba(255,255,255,0.7);
          }
        }

        .user-details {
          .user-name {
            display: block;
            font-size: 36rpx;
            font-weight: 600;
            color: #ffffff;
            margin-bottom: 8rpx;
          }

          .user-role {
            display: block;
            font-size: 24rpx;
            color: rgba(255,255,255,0.7);
          }
        }
      }

      .profile-btn {
        width: 60rpx;
        height: 60rpx;
        background: rgba(255,255,255,0.2);
        border-radius: 30rpx;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }

  .content-section {
    position: relative;
    top: -60rpx;
    padding: 0 30rpx;

    .quick-actions-card {
      background: #ffffff;
      border-radius: 20rpx;
      padding: 40rpx 30rpx;
      margin-bottom: 30rpx;
      box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.08);

      .actions-grid {
        display: flex;
        justify-content: space-between;

        .action-item {
          flex: 1;
          display: flex;
          flex-direction: column;
          align-items: center;
          padding: 20rpx 10rpx;
          border-radius: 16rpx;
          transition: all 0.3s ease;

          &:active {
            background-color: #f8f9fa;
            transform: scale(0.95);
          }

          .action-icon {
            width: 80rpx;
            height: 80rpx;
            background: rgba(255,107,157,0.1);
            border-radius: 20rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 16rpx;
          }

          .action-text {
            font-size: 24rpx;
            color: #333333;
            text-align: center;
          }
        }
      }
    }

    .function-menu {
      .menu-section {
        margin-bottom: 30rpx;

        .section-title {
          display: block;
          font-size: 28rpx;
          font-weight: 600;
          color: #666666;
          margin-bottom: 20rpx;
          padding-left: 10rpx;
        }

        .menu-items {
          background: #ffffff;
          border-radius: 16rpx;
          overflow: hidden;
          box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.05);

          .menu-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 32rpx 30rpx;
            border-bottom: 1rpx solid #f0f0f0;
            transition: all 0.3s ease;

            &:last-child {
              border-bottom: none;
            }

            &:active {
              background-color: #f8f9fa;
            }

            .menu-item-content {
              display: flex;
              align-items: center;

              .menu-icon {
                width: 60rpx;
                height: 60rpx;
                background: rgba(74,144,226,0.1);
                border-radius: 12rpx;
                display: flex;
                align-items: center;
                justify-content: center;
                margin-right: 24rpx;
              }

              .menu-text {
                font-size: 30rpx;
                color: #333333;
              }
            }
          }
        }
      }
    }
  }
}
</style>

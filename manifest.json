{"name": "若依移动端", "appid": "__UNI__25A9D80", "description": "", "versionName": "1.2.0", "versionCode": "100", "transformPx": false, "app-plus": {"usingComponents": true, "nvueCompiler": "uni-app", "splashscreen": {"alwaysShowBeforeRender": true, "waiting": true, "autoclose": true, "delay": 0}, "modules": {}, "distribute": {"android": {"permissions": ["<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>", "<uses-permission android:name=\"android.permission.VIBRATE\"/>", "<uses-permission android:name=\"android.permission.READ_LOGS\"/>", "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>", "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>", "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.CAMERA\"/>", "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>", "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>", "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>", "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>", "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>", "<uses-feature android:name=\"android.hardware.camera\"/>", "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>"]}, "ios": {"dSYMs": false}, "sdkConfigs": {}}}, "quickapp": {}, "mp-weixin": {"appid": "wxccd7e2a0911b3397", "setting": {"urlCheck": false, "es6": false, "minified": true, "postcss": true}, "optimization": {"subPackages": true}, "usingComponents": true}, "vueVersion": "3", "h5": {"template": "static/index.html", "devServer": {"port": 9090, "https": false}, "title": "RuoYi-App", "router": {"mode": "hash", "base": "./"}}}
<template>
  <view class="login-container">
    <!-- 背景装饰 -->
    <view class="background-decoration">
      <view class="decoration-circle circle-1"></view>
      <view class="decoration-circle circle-2"></view>
      <view class="decoration-circle circle-3"></view>
    </view>

    <!-- 顶部Logo区域 -->
    <view class="logo-section">
      <view class="welcome-text">
        <text class="app-title">若依移动端</text>
        <text class="app-subtitle">安全便捷的管理平台</text>
      </view>
    </view>

    <!-- 登录表单区域 -->
    <view class="form-section">
      <view class="form-card">
        <view class="form-header">
          <text class="form-title">账号登录</text>
          <text class="form-subtitle">请输入您的登录信息</text>
        </view>

        <view class="form-content">
          <view class="input-group">
            <view class="input-wrapper">
              <view class="input-icon">
                <uni-icons type="person" size="20" color="#999999"></uni-icons>
              </view>
              <input v-model="loginForm.username" class="form-input" type="text" placeholder="请输入账号" maxlength="30" />
            </view>
          </view>

          <view class="input-group">
            <view class="input-wrapper">
              <view class="input-icon">
                <uni-icons type="locked" size="20" color="#999999"></uni-icons>
              </view>
              <input v-model="loginForm.password" type="password" class="form-input" placeholder="请输入密码" maxlength="20" />
            </view>
          </view>

          <view class="input-group captcha-group" v-if="captchaEnabled">
            <view class="input-wrapper captcha-wrapper">
              <view class="input-icon">
                <uni-icons type="checkmarkempty" size="20" color="#999999"></uni-icons>
              </view>
              <input v-model="loginForm.code" type="number" class="form-input" placeholder="请输入验证码" maxlength="4" />
              <view class="captcha-image" @click="getCode">
                <image :src="codeUrl" class="captcha-img"></image>
              </view>
            </view>
          </view>

          <view class="login-actions">
            <button @click="handleLogin" class="login-button">
              <text class="button-text">登录</text>
            </button>
          </view>

          <view class="form-footer">
            <view class="register-link" v-if="register">
              <text class="footer-text">没有账号？</text>
              <text @click="handleUserRegister" class="link-text">立即注册</text>
            </view>
            <view class="agreement-text">
              <text class="footer-text">登录即代表同意</text>
              <text @click="handleUserAgrement" class="link-text">《用户协议》</text>
              <text @click="handlePrivacy" class="link-text">《隐私协议》</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
  import { getCodeImg } from '@/api/login'
  import { useConfigStore, useUserStore } from '@/store'
  import { ref, getCurrentInstance } from "vue"

  const { proxy } = getCurrentInstance()
  const globalConfig = useConfigStore().config
  const codeUrl = ref("")
  // 验证码开关
  const captchaEnabled = ref(true)
  // 用户注册开关
  const register = ref(false)
  const loginForm = ref({
    username: "",
    password: "",
    code: "",
    uuid: ""
  })

  // 用户注册
  function handleUserRegister() {
    proxy.$tab.redirectTo(`/pages/register`)
  }

  // 隐私协议
  function handlePrivacy() {
    let site = globalConfig.appInfo.agreements[0]
    proxy.$tab.navigateTo(`/pages/common/webview/index?title=${site.title}&url=${site.url}`)
  }

  // 用户协议
  function handleUserAgrement() {
    let site = globalConfig.appInfo.agreements[1]
    proxy.$tab.navigateTo(`/pages/common/webview/index?title=${site.title}&url=${site.url}`)
  }

  // 获取图形验证码
  function getCode() {
    getCodeImg().then(res => {
      captchaEnabled.value = res.captchaEnabled === undefined ? true : res.captchaEnabled
        if (captchaEnabled.value) {
          codeUrl.value = 'data:image/gif;base64,' + res.img
          loginForm.value.uuid = res.uuid
        }
    })
  }

  // 登录方法
  async function handleLogin() {
    if (loginForm.value.username === "") {
      proxy.$modal.msgError("请输入账号")
    } else if (loginForm.value.password === "") {
      proxy.$modal.msgError("请输入密码")
    } else if (loginForm.value.code === "" && captchaEnabled.value) {
      proxy.$modal.msgError("请输入验证码")
    } else {
      proxy.$modal.loading("登录中，请耐心等待...")
      pwdLogin()
    }
  }

  // 密码登录
  async function pwdLogin() {
    useUserStore().login(loginForm.value).then(() => {
      loginSuccess()
    }).catch((e) => {
		console.log(e)
      if (captchaEnabled.value) {
		proxy.$modal.msgError(e.msg)
        getCode()
      }
    }).finally(()=>{
		proxy.$modal.closeLoading()
	})
  }

  // 登录成功后，处理函数
  function loginSuccess(result) {
    // 设置用户信息
    useUserStore().getInfo().then(res => {
      proxy.$tab.reLaunch('/pages/index')
    })
  }

  getCode()
</script>

<style lang="scss" scoped>
page {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.login-container {
  position: relative;
  padding: 60rpx 40rpx;
  overflow: hidden;

  .background-decoration {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;

    .decoration-circle {
      position: absolute;
      border-radius: 50%;
      background: rgba(255,255,255,0.1);

      &.circle-1 {
        width: 200rpx;
        height: 200rpx;
        top: 10%;
        right: -50rpx;
        animation: float 6s ease-in-out infinite;
      }

      &.circle-2 {
        width: 150rpx;
        height: 150rpx;
        top: 30%;
        left: -30rpx;
        animation: float 8s ease-in-out infinite reverse;
      }

      &.circle-3 {
        width: 100rpx;
        height: 100rpx;
        bottom: 20%;
        right: 20%;
        animation: float 10s ease-in-out infinite;
      }
    }
  }

  .logo-section {
    position: relative;
    z-index: 2;
    text-align: center;
    padding: 60rpx 0 40rpx;

    .logo-wrapper {
      width: 160rpx;
      height: 160rpx;
      background: rgba(255,255,255,0.2);
      border-radius: 32rpx;
      margin: 0 auto 40rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      backdrop-filter: blur(10px);
      border: 2rpx solid rgba(255,255,255,0.3);

      .logo-image {
        width: 120rpx;
        height: 120rpx;
        border-radius: 24rpx;
      }
    }

    .welcome-text {
      .app-title {
        display: block;
        font-size: 48rpx;
        font-weight: 700;
        color: #ffffff;
        margin-bottom: 16rpx;
      }

      .app-subtitle {
        display: block;
        font-size: 28rpx;
        color: rgba(255,255,255,0.8);
      }
    }
  }

  .form-section {
    position: relative;
    z-index: 2;

    .form-card {
      background: rgba(255,255,255,0.95);
      border-radius: 24rpx;
      padding: 60rpx 40rpx;
      backdrop-filter: blur(20px);
      box-shadow: 0 8rpx 32rpx rgba(0,0,0,0.1);

      .form-header {
        text-align: center;
        margin-bottom: 60rpx;

        .form-title {
          display: block;
          font-size: 40rpx;
          font-weight: 600;
          color: #333333;
          margin-bottom: 16rpx;
        }

        .form-subtitle {
          display: block;
          font-size: 28rpx;
          color: #666666;
        }
      }

      .form-content {
        .input-group {
          margin-bottom: 40rpx;

          &.captcha-group {
            margin-bottom: 60rpx;
          }

          .input-wrapper {
            position: relative;
            background: #f8f9fa;
            border-radius: 16rpx;
            display: flex;
            align-items: center;
            padding: 0 24rpx;
            height: 96rpx;
            border: 2rpx solid transparent;
            transition: all 0.3s ease;

            &:focus-within {
              border-color: #667eea;
              background: #ffffff;
              box-shadow: 0 0 0 6rpx rgba(102,126,234,0.1);
            }

            .input-icon {
              margin-right: 20rpx;
            }

            .form-input {
              flex: 1;
              font-size: 32rpx;
              color: #333333;
              background: transparent;
              border: none;
              outline: none;

              &::placeholder {
                color: #999999;
              }
            }

            &.captcha-wrapper {
              .captcha-image {
                margin-left: 20rpx;

                .captcha-img {
                  width: 160rpx;
                  height: 60rpx;
                  border-radius: 8rpx;
                }
              }
            }
          }
        }

        .login-actions {
          margin-bottom: 40rpx;

          .login-button {
            width: 100%;
            height: 96rpx;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 16rpx;
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;

            &:active {
              transform: scale(0.98);
              box-shadow: 0 4rpx 20rpx rgba(102,126,234,0.3);
            }

            .button-text {
              font-size: 32rpx;
              font-weight: 600;
              color: #ffffff;
            }
          }
        }

        .form-footer {
          text-align: center;

          .register-link {
            margin-bottom: 30rpx;
          }

          .footer-text {
            font-size: 24rpx;
            color: #666666;
          }

          .link-text {
            font-size: 24rpx;
            color: #667eea;
            margin-left: 8rpx;

            &:active {
              opacity: 0.7;
            }
          }

          .agreement-text {
            line-height: 1.6;
          }
        }
      }
    }
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}
</style>
